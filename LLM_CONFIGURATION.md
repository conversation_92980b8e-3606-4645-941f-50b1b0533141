# LLM Configuration Guide

This document describes the new modular LLM configuration system in ComfyUI Video Chain.

## Overview

The LLM functionality has been refactored into a modular architecture that supports:
- Multiple LLM providers (OpenAI, OpenRouter)
- Configurable prompt templates
- Externalized system prompts
- Plugin-based service architecture

## Configuration Structure

### Basic Configuration

```json
{
  "llm_interpolation": true,
  "llm_config": {
    "service": "openai",
    "model": "gpt-4o",
    "temperature": 0.6,
    "max_tokens": 100,
    "api_base": "https://api.openai.com/v1/chat/completions",
    "timeout": 30000,
    "system_prompt": "Your system prompt here...",
    "user_prompt": "Your user prompt here...",
    "prompt_templates": {
      "previous_template": "(transitioning from: \"{{prompt}}\":0.5)",
      "current_template": "({{prompt}})",
      "next_template": "(transitioning to: \"{{prompt}}\":0.5)"
    }
  }
}
```

### Configuration Fields

#### Core LLM Settings
- `service`: LLM provider ("openai" or "openrouter")
- `model`: Model name (e.g., "gpt-4o", "anthropic/claude-3.5-sonnet")
- `temperature`: Creativity level (0.0-1.0)
- `max_tokens`: Maximum response length
- `api_base`: API endpoint URL
- `timeout`: Request timeout in milliseconds

#### Prompt Configuration
- `system_prompt`: The main instruction prompt for the LLM
- `user_prompt`: The user message sent to the LLM
- `prompt_templates`: Templates for simple interpolation

#### Template Variables
Templates support `{{variable}}` syntax:
- `{{prompt}}`: Replaced with the actual prompt text
- `{{context}}`: Replaced with scene context (in system_prompt)

## Supported LLM Providers

### OpenAI
```json
{
  "service": "openai",
  "model": "gpt-4o",
  "api_base": "https://api.openai.com/v1/chat/completions"
}
```

**Environment Variable**: `OPENAI_API_KEY`

**Supported Models**:
- gpt-4o
- gpt-4o-mini
- gpt-4-turbo
- gpt-3.5-turbo

### OpenRouter
```json
{
  "service": "openrouter",
  "model": "anthropic/claude-3.5-sonnet",
  "api_base": "https://openrouter.ai/api/v1/chat/completions"
}
```

**Environment Variable**: `OPENROUTER_API_KEY`

**Popular Models**:
- anthropic/claude-3.5-sonnet
- openai/gpt-4o
- meta-llama/llama-3.1-405b-instruct
- google/gemini-pro-1.5

## Prompt Templates

### Template Syntax
Use `{{prompt}}` as a placeholder for the actual prompt text:

```json
{
  "prompt_templates": {
    "previous_template": "(transitioning from: \"{{prompt}}\":0.5)",
    "current_template": "({{prompt}})",
    "next_template": "(transitioning to: \"{{prompt}}\":0.5)"
  }
}
```

### Interpolation Modes
- `none`: No interpolation
- `previous`: Add previous scene context
- `next`: Add next scene context  
- `both`: Add both previous and next scene context

## Backward Compatibility

The system maintains full backward compatibility:
- Old configuration files work without changes
- Default values match the original hardcoded settings
- LLM interpolation can be disabled with `"llm_interpolation": false`

## Environment Variables

Set the appropriate API key based on your chosen service:

```bash
# For OpenAI
export OPENAI_API_KEY="your-openai-api-key"

# For OpenRouter
export OPENROUTER_API_KEY="your-openrouter-api-key"
```

## Example Configurations

### Minimal Configuration (OpenAI)
```json
{
  "llm_interpolation": true,
  "llm_config": {
    "service": "openai"
  }
}
```

### Custom OpenRouter Configuration
```json
{
  "llm_interpolation": true,
  "llm_config": {
    "service": "openrouter",
    "model": "anthropic/claude-3.5-sonnet",
    "temperature": 0.7,
    "max_tokens": 150
  }
}
```

### Custom Prompt Templates
```json
{
  "llm_interpolation": true,
  "llm_config": {
    "service": "openai",
    "prompt_templates": {
      "previous_template": "[FROM: {{prompt}}]",
      "current_template": "{{prompt}}",
      "next_template": "[TO: {{prompt}}]"
    }
  }
}
```

## Troubleshooting

### Common Issues

1. **API Key Not Found**
   - Ensure the correct environment variable is set
   - Check that the variable name matches your service (OPENAI_API_KEY or OPENROUTER_API_KEY)

2. **Service Not Supported**
   - Verify the service name is "openai" or "openrouter"
   - Check for typos in the configuration

3. **Model Not Available**
   - Confirm the model name is correct for your chosen service
   - Check your API key has access to the specified model

4. **Timeout Errors**
   - Increase the timeout value in configuration
   - Check your internet connection

### Fallback Behavior

If LLM enhancement fails:
1. The system logs the error
2. Falls back to simple interpolation
3. Processing continues normally

This ensures video generation never fails due to LLM issues.
